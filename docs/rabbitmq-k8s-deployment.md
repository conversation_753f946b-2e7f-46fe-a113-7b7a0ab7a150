# RabbitMQ V3.11.3 Kubernetes 部署文档

## 概述

本文档提供了在Kubernetes集群中部署RabbitMQ V3.11.3的完整指南，包括单节点和集群模式的部署方案。

## 前置条件

- Kubernetes 集群版本 >= 1.19
- kubectl 命令行工具已配置
- 具有集群管理员权限
- 存储类(StorageClass)可用于持久化存储

## 部署架构

### 单节点部署
适用于开发和测试环境，提供基本的消息队列功能。

### 集群部署
适用于生产环境，提供高可用性和负载均衡。

## 快速部署

### 1. 创建命名空间

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: rabbitmq
  labels:
    name: rabbitmq
```

### 2. 创建ConfigMap

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: rabbitmq-config
  namespace: rabbitmq
data:
  enabled_plugins: |
    [rabbitmq_management,rabbitmq_peer_discovery_k8s].
  rabbitmq.conf: |
    cluster_formation.peer_discovery_backend = rabbit_peer_discovery_k8s
    cluster_formation.k8s.host = kubernetes.default.svc.cluster.local
    cluster_formation.node_cleanup.interval = 30
    cluster_formation.node_cleanup.only_log_warning = true
    cluster_partition_handling = autoheal
    queue_master_locator = min-masters
    loopback_users.guest = false
    default_user = admin
    default_pass = password123
```

### 3. 创建RBAC权限

```yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: rabbitmq
  namespace: rabbitmq
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: rabbitmq
  name: rabbitmq-peer-discovery-rbac
rules:
- apiGroups: [""]
  resources: ["endpoints"]
  verbs: ["get"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rabbitmq-peer-discovery-rbac
  namespace: rabbitmq
subjects:
- kind: ServiceAccount
  name: rabbitmq
  namespace: rabbitmq
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: rabbitmq-peer-discovery-rbac
```

### 4. 创建Service

```yaml
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-headless
  namespace: rabbitmq
spec:
  clusterIP: None
  ports:
  - name: amqp
    port: 5672
    targetPort: 5672
  - name: management
    port: 15672
    targetPort: 15672
  - name: prometheus
    port: 15692
    targetPort: 15692
  - name: epmd
    port: 4369
    targetPort: 4369
  selector:
    app: rabbitmq
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-service
  namespace: rabbitmq
spec:
  type: ClusterIP
  ports:
  - name: amqp
    port: 5672
    targetPort: 5672
  - name: management
    port: 15672
    targetPort: 15672
  selector:
    app: rabbitmq
```

### 5. 创建StatefulSet

```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: rabbitmq
  namespace: rabbitmq
spec:
  serviceName: rabbitmq-headless
  replicas: 3
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
    spec:
      serviceAccountName: rabbitmq
      terminationGracePeriodSeconds: 10
      nodeSelector:
        kubernetes.io/os: linux
      containers:
      - name: rabbitmq-k8s
        image: rabbitmq:3.11.3-management
        volumeMounts:
        - name: config-volume
          mountPath: /etc/rabbitmq
        - name: rabbitmq-data
          mountPath: /var/lib/rabbitmq/mnesia
        ports:
        - name: http
          protocol: TCP
          containerPort: 15672
        - name: amqp
          protocol: TCP
          containerPort: 5672
        - name: prometheus
          protocol: TCP
          containerPort: 15692
        - name: epmd
          protocol: TCP
          containerPort: 4369
        livenessProbe:
          exec:
            command: ["rabbitmq-diagnostics", "status"]
          initialDelaySeconds: 60
          periodSeconds: 60
          timeoutSeconds: 15
        readinessProbe:
          exec:
            command: ["rabbitmq-diagnostics", "ping"]
          initialDelaySeconds: 20
          periodSeconds: 60
          timeoutSeconds: 10
        imagePullPolicy: Always
        env:
        - name: MY_POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: MY_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: RABBITMQ_USE_LONGNAME
          value: "true"
        - name: K8S_SERVICE_NAME
          value: rabbitmq-headless
        - name: RABBITMQ_NODENAME
          value: rabbit@$(MY_POD_NAME).$(K8S_SERVICE_NAME).$(MY_POD_NAMESPACE).svc.cluster.local
        - name: K8S_HOSTNAME_SUFFIX
          value: .$(K8S_SERVICE_NAME).$(MY_POD_NAMESPACE).svc.cluster.local
        - name: RABBITMQ_ERLANG_COOKIE
          value: "mycookie"
        resources:
          limits:
            cpu: "2"
            memory: "2Gi"
          requests:
            cpu: "1"
            memory: "1Gi"
      volumes:
      - name: config-volume
        configMap:
          name: rabbitmq-config
          items:
          - key: rabbitmq.conf
            path: rabbitmq.conf
          - key: enabled_plugins
            path: enabled_plugins
  volumeClaimTemplates:
  - metadata:
      name: rabbitmq-data
      namespace: rabbitmq
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "standard"
      resources:
        requests:
          storage: 10Gi
```

## 部署步骤

1. **应用所有配置文件**
```bash
kubectl apply -f rabbitmq-namespace.yaml
kubectl apply -f rabbitmq-configmap.yaml
kubectl apply -f rabbitmq-rbac.yaml
kubectl apply -f rabbitmq-service.yaml
kubectl apply -f rabbitmq-statefulset.yaml
```

2. **验证部署状态**
```bash
kubectl get pods -n rabbitmq
kubectl get svc -n rabbitmq
kubectl get pvc -n rabbitmq
```

3. **检查集群状态**
```bash
kubectl exec -it rabbitmq-0 -n rabbitmq -- rabbitmqctl cluster_status
```

## 访问RabbitMQ

### 管理界面访问

1. **端口转发方式**
```bash
kubectl port-forward svc/rabbitmq-service 15672:15672 -n rabbitmq
```
然后访问: http://localhost:15672

2. **创建Ingress(可选)**
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rabbitmq-ingress
  namespace: rabbitmq
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: rabbitmq.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rabbitmq-service
            port:
              number: 15672
```

### 默认登录凭据
- 用户名: `admin`
- 密码: `password123`

## 应用连接配置

### 连接字符串示例
```
amqp://admin:<EMAIL>:5672/
```

### Java应用配置示例
```json
{
  "rabbitmq": {
    "host": "rabbitmq-service.rabbitmq.svc.cluster.local",
    "port": 5672,
    "username": "admin",
    "password": "password123",
    "virtualHost": "/"
  }
}
```

### Python应用配置示例
```json
{
  "connection_parameters": {
    "host": "rabbitmq-service.rabbitmq.svc.cluster.local",
    "port": 5672,
    "credentials": {
      "username": "admin",
      "password": "password123"
    }
  }
}
```

## 监控和维护

### 健康检查
```bash
# 检查集群状态
kubectl exec -it rabbitmq-0 -n rabbitmq -- rabbitmqctl cluster_status

# 检查队列状态
kubectl exec -it rabbitmq-0 -n rabbitmq -- rabbitmqctl list_queues

# 检查连接状态
kubectl exec -it rabbitmq-0 -n rabbitmq -- rabbitmqctl list_connections
```

### 日志查看
```bash
kubectl logs -f rabbitmq-0 -n rabbitmq
```

### 扩容操作
```bash
kubectl scale statefulset rabbitmq --replicas=5 -n rabbitmq
```

## 故障排除

### 常见问题

1. **Pod启动失败**
   - 检查存储类是否可用
   - 验证RBAC权限配置
   - 查看Pod日志

2. **集群节点无法加入**
   - 检查Erlang Cookie配置
   - 验证DNS解析
   - 确认网络策略

3. **持久化数据丢失**
   - 检查PVC状态
   - 验证存储类配置
   - 确认数据目录挂载

### 调试命令
```bash
# 查看详细事件
kubectl describe pod rabbitmq-0 -n rabbitmq

# 进入容器调试
kubectl exec -it rabbitmq-0 -n rabbitmq -- /bin/bash

# 检查配置文件
kubectl exec -it rabbitmq-0 -n rabbitmq -- cat /etc/rabbitmq/rabbitmq.conf
```

## 安全配置

### 更改默认密码
```bash
kubectl exec -it rabbitmq-0 -n rabbitmq -- rabbitmqctl change_password admin new_password
```

### 创建新用户
```bash
kubectl exec -it rabbitmq-0 -n rabbitmq -- rabbitmqctl add_user myuser mypassword
kubectl exec -it rabbitmq-0 -n rabbitmq -- rabbitmqctl set_user_tags myuser administrator
```

### 启用TLS(可选)
需要创建TLS证书并更新ConfigMap配置。

## 备份和恢复

### 数据备份
```bash
kubectl exec -it rabbitmq-0 -n rabbitmq -- rabbitmqctl export_definitions /tmp/definitions.json
kubectl cp rabbitmq-0:/tmp/definitions.json ./definitions.json -n rabbitmq
```

### 数据恢复
```bash
kubectl cp ./definitions.json rabbitmq-0:/tmp/definitions.json -n rabbitmq
kubectl exec -it rabbitmq-0 -n rabbitmq -- rabbitmqctl import_definitions /tmp/definitions.json
```

## 性能优化

### 资源配置建议
- CPU: 生产环境建议2-4核
- 内存: 生产环境建议4-8GB
- 存储: 根据消息量确定，建议SSD

### 配置优化
```yaml
# 在rabbitmq.conf中添加
vm_memory_high_watermark.relative = 0.6
disk_free_limit.relative = 2.0
cluster_partition_handling = autoheal
```

---

**注意**: 请根据实际环境调整配置参数，特别是存储类、资源限制和网络配置。在生产环境中部署前，建议先在测试环境中验证所有配置。
