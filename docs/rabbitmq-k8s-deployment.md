# RabbitMQ V3.11.3 Kubernetes 部署文档

## 概述

本文档提供了在Kubernetes集群中部署RabbitMQ V3.11.3的完整指南，包括单节点和集群模式的部署方案。

## 前置条件

- Kubernetes 集群版本 >= 1.19
- kubectl 命令行工具已配置
- 具有集群管理员权限

## 部署架构

### 单节点部署
适用于开发、测试和轻量级生产环境，提供完整的消息队列功能。

## 快速部署

### 1. 创建命名空间

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: rabbitmq
  labels:
    name: rabbitmq
```

### 2. 创建Secret

**方法一：使用kubectl命令创建（推荐）**
```bash
kubectl create secret generic rabbitmq-secret \
  --from-literal=username=admin \
  --from-literal=password=UjAwdEA5OTY= \
  -n rabbitmq
```

**方法二：使用YAML文件**
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: rabbitmq-secret
  namespace: rabbitmq
type: Opaque
data:
  # admin (base64编码)
  username: YWRtaW4=
  # your-secure-password (需要base64编码)
  password: UjAwdEA5OTY=
```

**生成base64编码的方法：**
```bash
echo -n "your-secure-password" | base64
```

### 3. 创建ConfigMap

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: rabbitmq-config
  namespace: rabbitmq
data:
  enabled_plugins: |
    [rabbitmq_management].
  rabbitmq.conf: |
    loopback_users.guest = false
```

### 4. 创建ServiceAccount

```yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: rabbitmq
  namespace: rabbitmq
```

### 5. 创建Service

```yaml
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-service
  namespace: rabbitmq
spec:
  type: ClusterIP
  ports:
  - name: amqp
    port: 5672
    targetPort: 5672
  - name: management
    port: 15672
    targetPort: 15672
  - name: prometheus
    port: 15692
    targetPort: 15692
  selector:
    app: rabbitmq
```

### 6. 创建Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rabbitmq
  namespace: rabbitmq
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
    spec:
      serviceAccountName: rabbitmq
      terminationGracePeriodSeconds: 10
      nodeSelector:
        kubernetes.io/os: linux
      containers:
      - name: rabbitmq
        image: registry.bcopstest.com:5000/blueking/rabbitmq:3.11.3-management
        volumeMounts:
        - name: config-volume
          mountPath: /etc/rabbitmq
        ports:
        - name: http
          protocol: TCP
          containerPort: 15672
        - name: amqp
          protocol: TCP
          containerPort: 5672
        - name: prometheus
          protocol: TCP
          containerPort: 15692
        livenessProbe:
          exec:
            command: ["rabbitmq-diagnostics", "status"]
          initialDelaySeconds: 60
          periodSeconds: 60
          timeoutSeconds: 15
        readinessProbe:
          exec:
            command: ["rabbitmq-diagnostics", "ping"]
          initialDelaySeconds: 20
          periodSeconds: 60
          timeoutSeconds: 10
        imagePullPolicy: Always
        env:
        - name: RABBITMQ_DEFAULT_USER
          valueFrom:
            secretKeyRef:
              name: rabbitmq-secret
              key: username
        - name: RABBITMQ_DEFAULT_PASS
          valueFrom:
            secretKeyRef:
              name: rabbitmq-secret
              key: password
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
      volumes:
      - name: config-volume
        configMap:
          name: rabbitmq-config
          items:
          - key: rabbitmq.conf
            path: rabbitmq.conf
          - key: enabled_plugins
            path: enabled_plugins

```

## 部署步骤

1. **应用所有配置文件**
```bash
kubectl apply -f rabbitmq-namespace.yaml
kubectl apply -f rabbitmq-secret.yaml
kubectl apply -f rabbitmq-configmap.yaml
kubectl apply -f rabbitmq-serviceaccount.yaml
kubectl apply -f rabbitmq-service.yaml
kubectl apply -f rabbitmq-deployment.yaml
```

2. **验证部署状态**
```bash
kubectl get pods -n rabbitmq
kubectl get svc -n rabbitmq
```

3. **检查RabbitMQ状态**
```bash
kubectl exec -it deployment/rabbitmq -n rabbitmq -- rabbitmqctl status
```

## 访问RabbitMQ

### 管理界面访问

1. **端口转发方式**
```bash
kubectl port-forward svc/rabbitmq-service 15672:15672 -n rabbitmq
```
然后访问: http://localhost:15672

2. **创建Ingress(可选)**
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rabbitmq-ingress
  namespace: rabbitmq
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: rabbitmq.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rabbitmq-service
            port:
              number: 15672
```

### 默认登录凭据
- 用户名: `admin`
- 密码: 在Secret中配置的密码（默认示例为`your-secure-password`）

**获取当前密码：**
```bash
kubectl get secret rabbitmq-secret -n rabbitmq -o jsonpath="{.data.password}" | base64 --decode
```

## 应用连接配置

### 连接字符串示例
```
amqp://admin:<EMAIL>:5672/
```

### Java应用配置示例
```json
{
  "rabbitmq": {
    "host": "rabbitmq-service.rabbitmq.svc.cluster.local",
    "port": 5672,
    "username": "admin",
    "password": "${RABBITMQ_PASSWORD}",
    "virtualHost": "/"
  }
}
```

### Python应用配置示例
```json
{
  "connection_parameters": {
    "host": "rabbitmq-service.rabbitmq.svc.cluster.local",
    "port": 5672,
    "credentials": {
      "username": "admin",
      "password": "${RABBITMQ_PASSWORD}"
    }
  }
}
```

**注意**: 应用中的密码应该通过环境变量或Secret挂载的方式获取，避免硬编码。

## 监控和维护

### 健康检查
```bash
# 检查RabbitMQ状态
kubectl exec -it deployment/rabbitmq -n rabbitmq -- rabbitmqctl status

# 检查队列状态
kubectl exec -it deployment/rabbitmq -n rabbitmq -- rabbitmqctl list_queues

# 检查连接状态
kubectl exec -it deployment/rabbitmq -n rabbitmq -- rabbitmqctl list_connections
```

### 日志查看
```bash
kubectl logs -f deployment/rabbitmq -n rabbitmq
```

### 重启操作
```bash
kubectl rollout restart deployment/rabbitmq -n rabbitmq
```

## 故障排除

### 常见问题

1. **Pod启动失败**
   - 验证RBAC权限配置
   - 查看Pod日志
   - 检查镜像拉取状态

2. **连接失败**
   - 检查Service配置
   - 验证网络策略
   - 确认端口映射

3. **Pod重启后数据丢失**
   - 这是正常现象，因为未使用持久化存储
   - 重要数据请通过应用层面进行备份

### 调试命令
```bash
# 查看详细事件
kubectl describe deployment rabbitmq -n rabbitmq

# 进入容器调试
kubectl exec -it deployment/rabbitmq -n rabbitmq -- /bin/bash

# 检查配置文件
kubectl exec -it deployment/rabbitmq -n rabbitmq -- cat /etc/rabbitmq/rabbitmq.conf
```

## 安全配置

### 密码管理最佳实践

1. **生成强密码**
```bash
# 生成随机密码
openssl rand -base64 32
```

2. **更新Secret中的密码**
```bash
# 方法一：重新创建Secret
kubectl delete secret rabbitmq-secret -n rabbitmq
kubectl create secret generic rabbitmq-secret \
  --from-literal=username=admin \
  --from-literal=password=new-secure-password \
  -n rabbitmq

# 方法二：直接更新Secret
kubectl patch secret rabbitmq-secret -n rabbitmq \
  -p='{"data":{"password":"'$(echo -n "new-secure-password" | base64)'"}}'
```

3. **重启Deployment使新密码生效**
```bash
kubectl rollout restart deployment/rabbitmq -n rabbitmq
```

### 更改默认密码（运行时）
```bash
kubectl exec -it deployment/rabbitmq -n rabbitmq -- rabbitmqctl change_password admin new_password
```

### 创建新用户
```bash
kubectl exec -it deployment/rabbitmq -n rabbitmq -- rabbitmqctl add_user myuser mypassword
kubectl exec -it deployment/rabbitmq -n rabbitmq -- rabbitmqctl set_user_tags myuser administrator
```

### 应用密码注入

**方法一：环境变量注入**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-app
spec:
  template:
    spec:
      containers:
      - name: app
        env:
        - name: RABBITMQ_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rabbitmq-secret
              key: password
```

**方法二：Volume挂载**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-app
spec:
  template:
    spec:
      containers:
      - name: app
        volumeMounts:
        - name: rabbitmq-secret
          mountPath: "/etc/rabbitmq-secret"
          readOnly: true
      volumes:
      - name: rabbitmq-secret
        secret:
          secretName: rabbitmq-secret
```

### 启用TLS(可选)
需要创建TLS证书并更新ConfigMap配置。

## 配置备份和恢复

### 配置备份
```bash
kubectl exec -it deployment/rabbitmq -n rabbitmq -- rabbitmqctl export_definitions /tmp/definitions.json
kubectl cp deployment/rabbitmq:/tmp/definitions.json ./definitions.json -n rabbitmq
```

### 配置恢复
```bash
kubectl cp ./definitions.json deployment/rabbitmq:/tmp/definitions.json -n rabbitmq
kubectl exec -it deployment/rabbitmq -n rabbitmq -- rabbitmqctl import_definitions /tmp/definitions.json
```

**注意**: 由于未使用持久化存储，消息数据在Pod重启后会丢失，只能备份和恢复队列、交换器等配置信息。

## 性能优化

### 资源配置建议
- CPU: 单节点建议1-2核
- 内存: 单节点建议1-2GB
- 网络: 确保网络连接稳定

### 配置优化
```yaml
# 在rabbitmq.conf中添加
vm_memory_high_watermark.relative = 0.6
disk_free_limit.relative = 1.0
```

---

**注意**:
- 此配置未使用持久化存储，适用于开发测试环境或对数据持久性要求不高的场景
- Pod重启后消息数据会丢失，请确保应用层面有相应的容错机制
- **安全提醒**：请务必修改默认密码，使用强密码，并通过Secret安全管理凭据
- 请根据实际环境调整配置参数，特别是资源限制和网络配置
- 在生产环境中部署前，建议先在测试环境中验证所有配置
- 生产环境建议启用TLS加密和更严格的访问控制
