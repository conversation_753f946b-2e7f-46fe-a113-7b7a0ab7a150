apiVersion: v1
kind: ConfigMap
metadata:
  name: rabbitmq-config
data:
  enabled_plugins: |
    [rabbitmq_management].
  rabbitmq.conf: |
    loopback_users.guest = false
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: rabbitmq
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-service
spec:
  type: ClusterIP
  ports:
    - name: amqp
      port: 5672
      targetPort: 5672
    - name: management
      port: 15672
      targetPort: 15672
    - name: prometheus
      port: 15692
      targetPort: 15692
  selector:
    app: rabbitmq
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rabbitmq
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: kubernetes.io/hostname
                    operator: In
                    values:
                      - bktest-clone-k8s-100-71-8-147
                      - bktest-clone-k8s-100-71-8-148
                      - bktest-clone-k8s-100-71-8-154
                      - bktest-clone-k8s-100-71-8-156
                      - bktest-clone-k8s-100-71-8-159
                      - bktest-clone-k8s-100-71-8-153
                      - bktest-clone-k8s-100-71-8-151
                      - bktest-clone-k8s-100-71-8-152
      serviceAccountName: rabbitmq
      terminationGracePeriodSeconds: 10
      nodeSelector:
        kubernetes.io/os: linux
      containers:
        - name: rabbitmq
          image: registry.bcopstest.com:5000/blueking/rabbitmq:3.11.3
          volumeMounts:
            - name: config-volume
              mountPath: /etc/rabbitmq
          ports:
            - name: http
              protocol: TCP
              containerPort: 15672
            - name: amqp
              protocol: TCP
              containerPort: 5672
            - name: prometheus
              protocol: TCP
              containerPort: 15692
          livenessProbe:
            exec:
              command: ["rabbitmq-diagnostics", "status"]
            initialDelaySeconds: 60
            periodSeconds: 60
            timeoutSeconds: 15
          readinessProbe:
            exec:
              command: ["rabbitmq-diagnostics", "ping"]
            initialDelaySeconds: 20
            periodSeconds: 60
            timeoutSeconds: 10
          imagePullPolicy: Always
          resources:
            limits:
              cpu: "1"
              memory: "1Gi"
            requests:
              cpu: "500m"
              memory: "512Mi"
      volumes:
        - name: config-volume
          configMap:
            name: rabbitmq-config
            items:
              - key: rabbitmq.conf
                path: rabbitmq.conf
              - key: enabled_plugins
                path: enabled_plugins
